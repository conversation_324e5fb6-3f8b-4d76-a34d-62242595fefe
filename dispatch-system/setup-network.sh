#!/bin/bash

# 网络配置设置脚本
# 用于配置局域网访问

# 设置颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 派单管理系统网络配置 ===${NC}"
echo

# 获取本机IP地址
LOCAL_IPS=$(hostname -I)
MAIN_IP=$(echo $LOCAL_IPS | awk '{print $1}')

echo -e "${BLUE}检测到的IP地址:${NC}"
echo "   $LOCAL_IPS"
echo -e "   主要IP: ${GREEN}$MAIN_IP${NC}"
echo

# 询问用户是否使用检测到的IP
echo -e "${YELLOW}是否使用检测到的主要IP地址 $MAIN_IP 进行配置? (y/n)${NC}"
read -r use_detected_ip

if [[ $use_detected_ip =~ ^[Yy]$ ]]; then
    SELECTED_IP=$MAIN_IP
else
    echo -e "${YELLOW}请输入要使用的IP地址:${NC}"
    read -r SELECTED_IP
    
    # 验证IP地址格式
    if [[ ! $SELECTED_IP =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        echo -e "${RED}IP地址格式不正确，使用默认IP: $MAIN_IP${NC}"
        SELECTED_IP=$MAIN_IP
    fi
fi

echo -e "${BLUE}使用IP地址: ${GREEN}$SELECTED_IP${NC}"
echo

# 创建前端环境配置文件
ENV_FILE="frontend/.env.local"
echo -e "${BLUE}创建前端环境配置文件: $ENV_FILE${NC}"

cat > "$ENV_FILE" << EOF
# 局域网访问配置
# 自动生成于 $(date)
VITE_API_BASE_URL=http://$SELECTED_IP:5000/api
EOF

echo -e "${GREEN}✓ 前端环境配置已创建${NC}"

# 检查后端配置
echo -e "${BLUE}检查后端配置...${NC}"
if grep -q "host='0.0.0.0'" backend/app.py; then
    echo -e "${GREEN}✓ 后端已配置为监听所有网络接口${NC}"
else
    echo -e "${YELLOW}! 后端可能需要配置网络监听${NC}"
fi

# 检查防火墙
echo -e "${BLUE}检查防火墙配置...${NC}"
if command -v firewall-cmd &> /dev/null; then
    if sudo firewall-cmd --state &> /dev/null; then
        if sudo firewall-cmd --list-ports | grep -q "5173\|5000-6000\|5000"; then
            echo -e "${GREEN}✓ 防火墙端口已开放${NC}"
        else
            echo -e "${YELLOW}! 需要开放防火墙端口${NC}"
            echo -e "${YELLOW}执行以下命令开放端口:${NC}"
            echo "   sudo firewall-cmd --add-port=5000/tcp --permanent"
            echo "   sudo firewall-cmd --add-port=5173/tcp --permanent"
            echo "   sudo firewall-cmd --reload"
            echo
            echo -e "${YELLOW}是否现在开放端口? (y/n)${NC}"
            read -r open_ports
            if [[ $open_ports =~ ^[Yy]$ ]]; then
                sudo firewall-cmd --add-port=5000/tcp --permanent
                sudo firewall-cmd --add-port=5173/tcp --permanent
                sudo firewall-cmd --reload
                echo -e "${GREEN}✓ 防火墙端口已开放${NC}"
            fi
        fi
    fi
fi

echo
echo -e "${GREEN}=== 配置完成 ===${NC}"
echo -e "${BLUE}局域网访问地址:${NC}"
echo -e "   前端: ${GREEN}http://$SELECTED_IP:5173${NC}"
echo -e "   后端: ${GREEN}http://$SELECTED_IP:5000${NC}"
echo
echo -e "${YELLOW}注意事项:${NC}"
echo "1. 重启前端服务以应用新配置: npm run dev"
echo "2. 确保后端服务正在运行: python backend/app.py"
echo "3. 确保局域网设备在同一网段"
echo "4. 如果仍无法访问，请检查路由器设置"
echo
echo -e "${BLUE}快速启动命令:${NC}"
echo "   ./start.sh"
