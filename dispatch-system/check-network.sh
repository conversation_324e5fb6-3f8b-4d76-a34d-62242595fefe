#!/bin/bash

# 网络配置检查脚本
# 用于诊断局域网访问问题

# 设置颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 派单管理系统网络配置检查 ===${NC}"
echo

# 1. 检查本机IP地址
echo -e "${BLUE}1. 本机IP地址:${NC}"
LOCAL_IPS=$(hostname -I)
echo "   $LOCAL_IPS"

# 获取主要的局域网IP
MAIN_IP=$(echo $LOCAL_IPS | awk '{print $1}')
echo -e "   主要IP: ${GREEN}$MAIN_IP${NC}"
echo

# 2. 检查端口占用情况
echo -e "${BLUE}2. 端口占用检查:${NC}"
if netstat -tuln | grep -q ":5173 "; then
    echo -e "   端口5173: ${GREEN}已占用${NC} (前端服务)"
else
    echo -e "   端口5173: ${YELLOW}未占用${NC}"
fi

if netstat -tuln | grep -q ":5000 "; then
    echo -e "   端口5000: ${GREEN}已占用${NC} (后端服务)"
else
    echo -e "   端口5000: ${YELLOW}未占用${NC}"
fi
echo

# 3. 检查防火墙状态
echo -e "${BLUE}3. 防火墙检查:${NC}"
if command -v firewall-cmd &> /dev/null; then
    if sudo firewall-cmd --state &> /dev/null; then
        echo -e "   防火墙状态: ${GREEN}运行中${NC}"
        echo "   开放端口: $(sudo firewall-cmd --list-ports)"
        
        # 检查5173端口是否开放
        if sudo firewall-cmd --list-ports | grep -q "5173\|5000-6000"; then
            echo -e "   端口5173: ${GREEN}已开放${NC}"
        else
            echo -e "   端口5173: ${RED}未开放${NC}"
            echo -e "   ${YELLOW}建议执行: sudo firewall-cmd --add-port=5173/tcp --permanent && sudo firewall-cmd --reload${NC}"
        fi
    else
        echo -e "   防火墙状态: ${YELLOW}未运行${NC}"
    fi
elif command -v ufw &> /dev/null; then
    UFW_STATUS=$(sudo ufw status | head -1)
    echo "   UFW状态: $UFW_STATUS"
else
    echo -e "   ${YELLOW}未检测到防火墙管理工具${NC}"
fi
echo

# 4. 检查网络连通性
echo -e "${BLUE}4. 网络连通性测试:${NC}"
if ping -c 1 -W 1 $MAIN_IP &> /dev/null; then
    echo -e "   本机连通性: ${GREEN}正常${NC}"
else
    echo -e "   本机连通性: ${RED}异常${NC}"
fi
echo

# 5. 显示访问地址
echo -e "${BLUE}5. 局域网访问地址:${NC}"
echo -e "   前端: ${GREEN}http://$MAIN_IP:5173${NC}"
echo -e "   后端: ${GREEN}http://$MAIN_IP:5000${NC}"
echo

# 6. 常见问题解决方案
echo -e "${BLUE}6. 常见问题解决方案:${NC}"
echo -e "   ${YELLOW}问题1: 局域网无法访问${NC}"
echo "   解决: 确保防火墙开放5173端口"
echo "   命令: sudo firewall-cmd --add-port=5173/tcp --permanent"
echo "        sudo firewall-cmd --reload"
echo
echo -e "   ${YELLOW}问题2: 服务未启动${NC}"
echo "   解决: 使用启动脚本启动服务"
echo "   命令: ./start.sh"
echo
echo -e "   ${YELLOW}问题3: 端口被占用${NC}"
echo "   解决: 查找并停止占用端口的进程"
echo "   命令: sudo netstat -tulpn | grep :5173"
echo "        sudo kill -9 <PID>"
echo

# 7. 实时监控
echo -e "${BLUE}7. 实时端口监控:${NC}"
echo "   使用以下命令监控端口状态:"
echo "   watch 'netstat -tuln | grep -E \":(5173|5000) \"'"
echo

echo -e "${GREEN}=== 检查完成 ===${NC}"
echo -e "如果局域网仍无法访问，请检查:"
echo "1. 路由器是否阻止了内网通信"
echo "2. 客户端设备是否在同一网段"
echo "3. 是否有其他安全软件阻止访问"
