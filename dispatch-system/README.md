# 派单管理系统

一个基于Vue3 + Python Flask的现代化派单管理系统，支持工单创建、分配、跟踪和管理。

## 技术栈

### 前端
- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript超集
- **Element Plus** - 基于Vue 3的组件库
- **Vue Router** - 官方路由管理器
- **Pinia** - Vue的状态管理库
- **Axios** - HTTP客户端
- **Vite** - 现代化构建工具

### 后端
- **Python Flask** - 轻量级Web框架
- **SQLite** - 轻量级数据库
- **Flask-SQLAlchemy** - ORM框架
- **Flask-JWT-Extended** - JWT认证
- **Flask-CORS** - 跨域资源共享

## 功能特性

- 🔐 **用户认证** - 登录/登出，角色权限管理
- 📋 **工单管理** - 创建、编辑、查看、删除工单
- 👥 **人员管理** - 管理维修人员信息
- 📊 **仪表板** - 工单统计和快速操作
- 🔍 **搜索筛选** - 按状态、关键词搜索工单
- 📱 **响应式设计** - 支持移动端访问
- 🎨 **现代化UI** - 基于Element Plus的美观界面

## 快速开始

### 环境要求

- Node.js 16+ 
- Python 3.8+
- npm 或 yarn

### 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd dispatch-system
   ```

2. **一键启动**
   ```bash
   chmod +x start.sh
   ./start.sh
   ```

3. **访问应用**
   - 前端地址: http://localhost:5173
   - 后端API: http://localhost:5000

### 手动启动

#### 启动后端
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python app.py
```

#### 启动前端
```bash
cd frontend
npm install
npm run dev
```

## 默认账户

系统预设了以下测试账户：

- **管理员**: admin / admin123
- **经理**: manager / manager123  
- **员工**: user / user123

## 项目结构

```
dispatch-system/
├── backend/                 # 后端代码
│   ├── app/                # 应用模块
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由处理
│   │   └── utils/          # 工具函数
│   ├── app.py              # 应用入口
│   └── requirements.txt    # Python依赖
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── components/     # Vue组件
│   │   ├── views/          # 页面视图
│   │   ├── stores/         # Pinia状态管理
│   │   ├── router/         # 路由配置
│   │   └── assets/         # 静态资源
│   ├── package.json        # Node.js依赖
│   └── vite.config.ts      # Vite配置
├── start.sh                # 启动脚本
├── stop.sh                 # 停止脚本
└── README.md               # 项目说明
```

## 开发说明

### 前端开发

- 使用Vue 3 Composition API
- TypeScript提供类型安全
- Element Plus组件库
- 响应式设计，支持移动端

### 后端开发

- RESTful API设计
- JWT token认证
- SQLAlchemy ORM
- 角色权限控制

### 数据库

系统使用SQLite数据库，包含以下主要表：

- `users` - 用户表
- `work_orders` - 工单表  
- `personnel` - 人员表
- `process_records` - 处理记录表

## API文档

主要API端点：

- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息
- `GET /api/work-orders` - 获取工单列表
- `POST /api/work-orders` - 创建工单
- `GET /api/work-orders/{id}` - 获取工单详情
- `PUT /api/work-orders/{id}` - 更新工单
- `DELETE /api/work-orders/{id}` - 删除工单

## 部署

### 生产环境部署

1. **构建前端**
   ```bash
   cd frontend
   npm run build
   ```

2. **配置后端**
   - 修改数据库配置
   - 设置环境变量
   - 配置Web服务器(如Nginx)

3. **启动服务**
   ```bash
   # 使用gunicorn启动后端
   gunicorn -w 4 -b 0.0.0.0:5000 app:app
   ```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
