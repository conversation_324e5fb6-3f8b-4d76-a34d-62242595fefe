#!/bin/bash

GREEN='\033[0;32m'
NC='\033[0m' # No Color

echo -e "${G<PERSON><PERSON>}停止调度系统...${NC}"

# 检查并停止后端
if [ -f ".backend_pid" ]; then
  BACKEND_PID=$(cat .backend_pid)
  if ps -p $BACKEND_PID > /dev/null; then
    kill $BACKEND_PID
    echo "后端服务已停止"
  else
    echo "后端服务未运行"
  fi
  rm .backend_pid
fi

# 检查并停止前端
if [ -f ".frontend_pid" ]; then
  FRONTEND_PID=$(cat .frontend_pid)
  if ps -p $FRONTEND_PID > /dev/null; then
    kill $FRONTEND_PID
    echo "前端服务已停止"
  else
    echo "前端服务未运行"
  fi
  rm .frontend_pid
fi

echo -e "${GREEN}所有服务已停止${NC}"