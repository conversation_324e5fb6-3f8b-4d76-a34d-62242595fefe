<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证状态测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
    </style>
</head>
<body>
    <h1>认证状态持久化测试</h1>
    
    <div class="test-section">
        <h3>当前状态</h3>
        <div id="current-status" class="status info">检查中...</div>
        <button class="btn-info" onclick="checkStatus()">刷新状态</button>
    </div>
    
    <div class="test-section">
        <h3>localStorage 内容</h3>
        <div id="localStorage-content" class="status info">检查中...</div>
        <button class="btn-info" onclick="checkLocalStorage()">刷新 localStorage</button>
    </div>
    
    <div class="test-section">
        <h3>测试操作</h3>
        <button class="btn-primary" onclick="simulateLogin()">模拟登录</button>
        <button class="btn-danger" onclick="simulateLogout()">模拟登出</button>
        <button class="btn-info" onclick="testRefresh()">测试页面刷新</button>
    </div>
    
    <div class="test-section">
        <h3>测试结果</h3>
        <div id="test-results"></div>
    </div>

    <script>
        function checkStatus() {
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            const statusDiv = document.getElementById('current-status');
            
            if (token && user) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `
                    <strong>已登录</strong><br>
                    Token: ${token.substring(0, 20)}...<br>
                    用户: ${JSON.parse(user).name || '未知'}
                `;
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '<strong>未登录</strong>';
            }
        }
        
        function checkLocalStorage() {
            const content = document.getElementById('localStorage-content');
            const items = [];
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                items.push(`<strong>${key}:</strong> ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}`);
            }
            
            content.innerHTML = items.length > 0 ? items.join('<br>') : '无数据';
        }
        
        function simulateLogin() {
            const mockToken = 'mock_token_' + Date.now();
            const mockUser = {
                id: 1,
                username: 'testuser',
                name: '测试用户',
                email: '<EMAIL>',
                role: 'user'
            };
            
            localStorage.setItem('token', mockToken);
            localStorage.setItem('user', JSON.stringify(mockUser));
            
            addTestResult('模拟登录完成', 'success');
            checkStatus();
            checkLocalStorage();
        }
        
        function simulateLogout() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            
            addTestResult('模拟登出完成', 'success');
            checkStatus();
            checkLocalStorage();
        }
        
        function testRefresh() {
            addTestResult('即将刷新页面，请观察认证状态是否保持...', 'info');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
        
        function addTestResult(message, type) {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = `[${timestamp}] ${message}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        // 页面加载时检查状态
        window.onload = function() {
            checkStatus();
            checkLocalStorage();
            
            // 检查是否是刷新后的页面
            if (performance.navigation.type === 1) {
                addTestResult('页面已刷新，检查认证状态是否保持', 'info');
            }
        };
    </script>
</body>
</html>
