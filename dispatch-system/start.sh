#!/bin/bash

# 设置颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}启动调度系统...${NC}"

# 启动后端
echo -e "${BLUE}启动后端服务...${NC}"
cd backend
# 检查是否存在虚拟环境
if [ -d "venv" ]; then
  source venv/bin/activate
else
  echo "创建Python虚拟环境..."
  python -m venv venv
  source venv/bin/activate
  pip install -r requirements.txt
fi

# 后台运行Flask应用
python app.py &
BACKEND_PID=$!
echo -e "${GREEN}后端服务已启动，PID: $BACKEND_PID${NC}"

# 启动前端
echo -e "${BLUE}启动前端服务...${NC}"
cd ../frontend
# 检查是否安装了依赖
if [ ! -d "node_modules" ]; then
  echo "安装前端依赖..."
  npm install
fi

# 启动前端开发服务器
npm run dev &
FRONTEND_PID=$!
echo -e "${GREEN}前端服务已启动，PID: $FRONTEND_PID${NC}"

echo -e "${GREEN}调度系统已启动!${NC}"
echo -e "${BLUE}后端地址: http://localhost:5000${NC}"
echo -e "${BLUE}前端地址: http://localhost:5173${NC}"

# 保存PID以便后续可以关闭服务
echo "$BACKEND_PID" > .backend_pid
echo "$FRONTEND_PID" > .frontend_pid

# 等待用户按Ctrl+C
echo "按 Ctrl+C 停止服务..."
trap "kill $BACKEND_PID $FRONTEND_PID; echo -e '${GREEN}服务已停止${NC}'; exit" INT
wait
