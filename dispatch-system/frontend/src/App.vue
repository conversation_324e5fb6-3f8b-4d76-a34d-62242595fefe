<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import MainLayout from '@/components/MainLayout.vue'

const route = useRoute()
const authStore = useAuthStore()

// 不需要布局的页面（如登录页）
const noLayoutPages = ['/login']
const needsLayout = computed(() => !noLayoutPages.includes(route.path))
</script>

<template>
  <div id="app">
    <MainLayout v-if="needsLayout && authStore.isLoggedIn">
      <router-view />
    </MainLayout>
    <router-view v-else />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
  background-color: #f5f5f5;
}

#app {
  min-height: 100vh;
}

/* Element Plus 响应式优化 */
.el-table {
  font-size: 14px;
}

.el-card {
  margin-bottom: 20px;
}

/* 响应式表格 */
@media (max-width: 768px) {
  .el-table {
    font-size: 12px;
  }

  .el-table .el-table__cell {
    padding: 8px 4px;
  }

  .el-card {
    margin-bottom: 15px;
  }

  .el-card .el-card__body {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .el-table {
    font-size: 11px;
  }

  .el-table .el-table__cell {
    padding: 6px 2px;
  }

  .el-card .el-card__body {
    padding: 10px;
  }

  .el-button {
    padding: 8px 12px;
    font-size: 12px;
  }
}
</style>
