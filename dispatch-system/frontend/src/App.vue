<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import MainLayout from '@/components/MainLayout.vue'

const route = useRoute()
const authStore = useAuthStore()

// 不需要布局的页面（如登录页）
const noLayoutPages = ['/login']
const needsLayout = computed(() => !noLayoutPages.includes(route.path))
</script>

<template>
  <div id="app">
    <MainLayout v-if="needsLayout && authStore.isLoggedIn">
      <router-view />
    </MainLayout>
    <router-view v-else />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
  background-color: #f5f5f5;
}

#app {
  min-height: 100vh;
}
</style>
