import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/work-orders',
      name: 'work-orders',
      component: () => import('../views/WorkOrdersView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/work-orders/create',
      name: 'work-order-create',
      component: () => import('../views/WorkOrderCreateView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/work-orders/:id',
      name: 'work-order-detail',
      component: () => import('../views/WorkOrderDetailView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/personnel',
      name: 'personnel',
      component: () => import('../views/PersonnelView.vue'),
      meta: { requiresAuth: true, requiresRole: ['admin', 'manager'] }
    },
    {
      path: '/settings',
      name: 'settings',
      component: () => import('../views/SettingsView.vue'),
      meta: { requiresAuth: true }
    }
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 初始化用户信息
  if (authStore.token && !authStore.user) {
    try {
      await authStore.initUser()
    } catch (error) {
      // 如果初始化失败，清除token并跳转到登录页
      authStore.logout()
      if (to.path !== '/login') {
        next('/login')
        return
      }
    }
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth && !authStore.isLoggedIn) {
    next('/login')
    return
  }

  // 检查是否需要游客状态（如登录页）
  if (to.meta.requiresGuest && authStore.isLoggedIn) {
    next('/dashboard')
    return
  }

  // 检查角色权限
  if (to.meta.requiresRole && authStore.user) {
    const requiredRoles = to.meta.requiresRole as string[]
    if (!requiredRoles.includes(authStore.user.role)) {
      next('/dashboard')
      return
    }
  }

  next()
})

export default router
