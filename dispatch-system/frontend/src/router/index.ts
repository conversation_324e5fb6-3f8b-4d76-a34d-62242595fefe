import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('@/views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/work-orders',
      name: 'work-orders',
      component: () => import('@/views/WorkOrdersView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/work-orders/create',
      name: 'work-order-create',
      component: () => import('@/views/WorkOrderCreateView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/work-orders/:id/edit',
      name: 'WorkOrderEdit',
      component: () => import('@/views/WorkOrderEditView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/work-orders/:id',
      name: 'work-order-detail',
      component: () => import('@/views/WorkOrderDetailView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/groups',
      name: 'groups',
      component: () => import('@/views/GroupsView.vue'),
      meta: { requiresAuth: true, requiresRole: ['admin', 'manager'] }
    },
    {
      path: '/personnel',
      name: 'personnel',
      component: () => import('@/views/PersonnelView.vue'),
      meta: { requiresAuth: true, requiresRole: ['admin', 'manager'] }
    },
    {
      path: '/vehicles',
      name: 'vehicles',
      component: () => import('@/views/VehiclesView.vue'),
      meta: { requiresAuth: true, requiresRole: ['admin', 'manager'] }
    },
    {
      path: '/materials',
      name: 'materials',
      component: () => import('@/views/MaterialsView.vue'),
      meta: { requiresAuth: true, requiresRole: ['admin', 'manager'] }
    },
    {
      path: '/users',
      name: 'users',
      component: () => import('@/views/UsersView.vue'),
      meta: { requiresAuth: true, requiresRole: ['admin'] }
    },
    {
      path: '/settings',
      name: 'settings',
      component: () => import('@/views/SettingsView.vue'),
      meta: { requiresAuth: true }
    }
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 初始化用户信息
  if (authStore.token && !authStore.user) {
    await authStore.initUser()
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth && !authStore.isLoggedIn) {
    next('/login')
    return
  }

  // 检查是否需要游客状态（如登录页）
  if (to.meta.requiresGuest && authStore.isLoggedIn) {
    // 根据用户角色跳转到相应页面
    if (authStore.isAdmin) {
      next('/work-orders')
    } else if (authStore.isManager) {
      next('/work-orders')
    } else {
      next('/dashboard')
    }
    return
  }

  // 检查角色权限
  if (to.meta.requiresRole && authStore.user) {
    const requiredRoles = to.meta.requiresRole as string[]
    if (!requiredRoles.includes(authStore.user.role)) {
      next('/dashboard')
      return
    }
  }

  next()
})

export default router
