<template>
  <Layout>
    <div class="materials">
      <div class="page-header">
        <h1>材料管理</h1>
        <el-button type="primary">添加材料</el-button>
      </div>
      <el-card>
        <p>材料管理页面</p>
      </el-card>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import Layout from '@/components/Layout.vue'
</script>

<style scoped>
.materials {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}
</style>
