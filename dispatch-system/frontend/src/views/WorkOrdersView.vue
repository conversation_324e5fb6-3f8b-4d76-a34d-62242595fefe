<template>
  <Layout>
    <div class="work-orders">
      <div class="page-header">
        <h1>工单管理</h1>
        <el-button type="primary" @click="$router.push('/work-orders/create')">
          <el-icon><Plus /></el-icon>
          创建工单
        </el-button>
      </div>
      
      <el-card>
        <div class="filters">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input
                v-model="filters.search"
                placeholder="搜索工单标题"
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
            <el-col :span="4">
              <el-select v-model="filters.status" placeholder="状态" clearable @change="loadWorkOrders">
                <el-option label="待处理" value="pending" />
                <el-option label="已分配" value="assigned" />
                <el-option label="处理中" value="in_progress" />
                <el-option label="已完成" value="completed" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="filters.priority" placeholder="优先级" clearable @change="loadWorkOrders">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
                <el-option label="紧急" value="urgent" />
              </el-select>
            </el-col>
          </el-row>
        </div>
        
        <el-table :data="workOrders" v-loading="loading" style="width: 100%">
          <el-table-column prop="order_number" label="工单编号" width="150" />
          <el-table-column prop="title" label="标题" min-width="200" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="assignee_name" label="负责人" width="120" />
          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="viewDetail(row.id)">
                查看
              </el-button>
              <el-button type="warning" size="small" @click="editWorkOrder(row.id)">
                编辑
              </el-button>
              <el-button
                v-if="authStore.isAdmin || authStore.isManager || row.creator_id === authStore.user?.id"
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadWorkOrders"
            @current-change="loadWorkOrders"
          />
        </div>
      </el-card>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import {
  getWorkOrders,
  deleteWorkOrder,
  type WorkOrder,
  type WorkOrderListParams
} from '@/api/workOrders'
import { useAuthStore } from '@/stores/auth'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)
const workOrders = ref<WorkOrder[]>([])

const filters = reactive({
  search: '',
  status: '',
  priority: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    assigned: 'info',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    assigned: '已分配',
    in_progress: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger'
  }
  return types[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

const loadWorkOrders = async () => {
  loading.value = true
  try {
    const params: WorkOrderListParams = {
      page: pagination.page,
      per_page: pagination.size,
      search: filters.search || undefined,
      status: filters.status || undefined,
      priority: filters.priority || undefined
    }

    const response = await getWorkOrders(params)
    workOrders.value = response.work_orders
    pagination.total = response.total
  } catch (error) {
    console.error('加载工单列表失败:', error)
    ElMessage.error('加载工单列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadWorkOrders()
}

const viewDetail = (id: number) => {
  router.push(`/work-orders/${id}`)
}

const editWorkOrder = (id: number) => {
  router.push(`/work-orders/${id}/edit`)
}

const handleDelete = async (workOrder: WorkOrder) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除工单"${workOrder.title}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteWorkOrder(workOrder.id)
    ElMessage.success('工单删除成功')
    loadWorkOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除工单失败:', error)
      ElMessage.error('删除工单失败')
    }
  }
}

onMounted(() => {
  loadWorkOrders()
})
</script>

<style scoped>
.work-orders {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.filters {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
