<template>
  <Layout>
    <div class="groups">
      <div class="page-header">
        <h1>分组管理</h1>
        <el-button type="primary">
          <el-icon><Plus /></el-icon>
          添加分组
        </el-button>
      </div>
      
      <el-card>
        <el-table :data="groups" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="分组名称" />
          <el-table-column prop="description" label="描述" />
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button type="primary" size="small">编辑</el-button>
              <el-button type="danger" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'

const groups = ref([
  { id: 1, name: '技术部', description: '负责技术开发' },
  { id: 2, name: '运维部', description: '负责系统运维' }
])
</script>

<style scoped>
.groups {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}
</style>
