<template>
  <Layout>
    <div class="work-order-edit">
      <div class="page-header">
        <h1>编辑工单</h1>
        <el-button @click="$router.back()">返回</el-button>
      </div>
      
      <div v-if="loading" v-loading="loading" style="height: 200px;"></div>
      
      <el-card v-else-if="workOrder">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          label-position="left"
        >
          <el-form-item label="工单编号">
            <el-input v-model="workOrder.order_number" disabled />
          </el-form-item>
          
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入工单标题" />
          </el-form-item>
          
          <el-form-item label="描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="4"
              placeholder="请输入工单描述"
            />
          </el-form-item>
          
          <el-form-item label="状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择状态">
              <el-option label="待处理" value="pending" />
              <el-option label="已分配" value="assigned" />
              <el-option label="处理中" value="in_progress" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="form.priority" placeholder="请选择优先级">
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
              <el-option label="紧急" value="urgent" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="负责人" prop="assignee_id">
            <el-select v-model="form.assignee_id" placeholder="请选择负责人" clearable>
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="user.username"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="计划开始时间" prop="scheduled_start">
            <el-date-picker
              v-model="form.scheduled_start"
              type="datetime"
              placeholder="请选择计划开始时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
          
          <el-form-item label="计划结束时间" prop="scheduled_end">
            <el-date-picker
              v-model="form.scheduled_end"
              type="datetime"
              placeholder="请选择计划结束时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
          
          <!-- 自定义字段 -->
          <div v-if="customFields.length > 0" class="custom-fields">
            <el-divider content-position="left">自定义字段</el-divider>
            
            <el-form-item
              v-for="field in customFields"
              :key="field.id"
              :label="field.label"
              :prop="`custom_fields.${field.name}`"
              :rules="field.required ? [{ required: true, message: `请填写${field.label}`, trigger: 'blur' }] : []"
            >
              <!-- 文本输入 -->
              <el-input
                v-if="field.field_type === 'text'"
                v-model="form.custom_fields[field.name]"
                :placeholder="`请输入${field.label}`"
              />
              
              <!-- 多行文本 -->
              <el-input
                v-else-if="field.field_type === 'textarea'"
                v-model="form.custom_fields[field.name]"
                type="textarea"
                :rows="3"
                :placeholder="`请输入${field.label}`"
              />
              
              <!-- 数字输入 -->
              <el-input-number
                v-else-if="field.field_type === 'number'"
                v-model="form.custom_fields[field.name]"
                :placeholder="`请输入${field.label}`"
                style="width: 100%"
              />
              
              <!-- 日期选择 -->
              <el-date-picker
                v-else-if="field.field_type === 'date'"
                v-model="form.custom_fields[field.name]"
                type="date"
                :placeholder="`请选择${field.label}`"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
              
              <!-- 下拉选择 -->
              <el-select
                v-else-if="field.field_type === 'select'"
                v-model="form.custom_fields[field.name]"
                :placeholder="`请选择${field.label}`"
                style="width: 100%"
              >
                <el-option
                  v-for="option in field.options"
                  :key="option"
                  :label="option"
                  :value="option"
                />
              </el-select>
              
              <!-- 布尔值 -->
              <el-switch
                v-else-if="field.field_type === 'boolean'"
                v-model="form.custom_fields[field.name]"
              />
            </el-form-item>
          </div>
          
          <el-form-item>
            <el-button type="primary" @click="handleSubmit" :loading="submitting">
              保存修改
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      
      <el-card v-else>
        <el-empty description="工单不存在" />
      </el-card>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import Layout from '@/components/Layout.vue'
import { 
  getWorkOrder, 
  updateWorkOrder, 
  getWorkOrderFields, 
  type WorkOrder, 
  type WorkOrderField,
  type UpdateWorkOrderData 
} from '@/api/workOrders'
import { getUsers, type User } from '@/api/users'

const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()

const loading = ref(false)
const submitting = ref(false)
const workOrder = ref<WorkOrder | null>(null)
const users = ref<User[]>([])
const customFields = ref<WorkOrderField[]>([])

const form = reactive({
  title: '',
  description: '',
  status: 'pending',
  priority: 'medium',
  assignee_id: null as number | null,
  scheduled_start: '',
  scheduled_end: '',
  custom_fields: {} as Record<string, any>
})

const rules: FormRules = {
  title: [
    { required: true, message: '请输入工单标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入工单描述', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

const loadWorkOrder = async () => {
  const id = Number(route.params.id)
  if (!id) {
    ElMessage.error('无效的工单ID')
    return
  }
  
  loading.value = true
  try {
    const response = await getWorkOrder(id)
    workOrder.value = response.work_order
    
    // 填充表单数据
    form.title = workOrder.value.title
    form.description = workOrder.value.description
    form.status = workOrder.value.status
    form.priority = workOrder.value.priority
    form.assignee_id = workOrder.value.assignee_id || null
    form.scheduled_start = workOrder.value.scheduled_start || ''
    form.scheduled_end = workOrder.value.scheduled_end || ''
    form.custom_fields = { ...workOrder.value.custom_fields }
  } catch (error) {
    console.error('加载工单详情失败:', error)
    ElMessage.error('加载工单详情失败')
  } finally {
    loading.value = false
  }
}

const loadUsers = async () => {
  try {
    const response = await getUsers({ per_page: 100 })
    users.value = response.users.filter(user => user.is_active)
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  }
}

const loadCustomFields = async () => {
  try {
    const response = await getWorkOrderFields()
    customFields.value = response.fields.filter(field => field.is_active)
  } catch (error) {
    console.error('加载自定义字段失败:', error)
  }
}

const handleSubmit = async () => {
  if (!formRef.value || !workOrder.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    const data: UpdateWorkOrderData = {
      title: form.title,
      description: form.description,
      status: form.status,
      priority: form.priority,
      assignee_id: form.assignee_id || undefined,
      custom_fields: form.custom_fields
    }
    
    await updateWorkOrder(workOrder.value.id, data)
    ElMessage.success('工单更新成功')
    router.push('/work-orders')
  } catch (error) {
    console.error('更新工单失败:', error)
    ElMessage.error('更新工单失败')
  } finally {
    submitting.value = false
  }
}

const handleReset = () => {
  if (workOrder.value) {
    form.title = workOrder.value.title
    form.description = workOrder.value.description
    form.status = workOrder.value.status
    form.priority = workOrder.value.priority
    form.assignee_id = workOrder.value.assignee_id || null
    form.scheduled_start = workOrder.value.scheduled_start || ''
    form.scheduled_end = workOrder.value.scheduled_end || ''
    form.custom_fields = { ...workOrder.value.custom_fields }
  }
}

onMounted(() => {
  loadWorkOrder()
  loadUsers()
  loadCustomFields()
})
</script>

<style scoped>
.work-order-edit {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.custom-fields {
  margin-top: 20px;
}

.custom-fields .el-divider {
  margin: 20px 0;
}
</style>
