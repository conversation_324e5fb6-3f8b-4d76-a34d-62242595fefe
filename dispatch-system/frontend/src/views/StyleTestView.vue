<template>
  <div class="style-test page-container">
    <div class="page-header">
      <h2>样式测试页面</h2>
      <p>用于测试不同屏幕尺寸下的显示效果</p>
    </div>

    <!-- 屏幕尺寸信息 -->
    <el-card>
      <template #header>
        <span>当前屏幕信息</span>
      </template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="屏幕宽度">{{ screenWidth }}px</el-descriptions-item>
        <el-descriptions-item label="屏幕高度">{{ screenHeight }}px</el-descriptions-item>
        <el-descriptions-item label="设备类型">{{ deviceType }}</el-descriptions-item>
        <el-descriptions-item label="容器宽度">{{ containerWidth }}px</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 网格布局测试 -->
    <el-card>
      <template #header>
        <span>网格布局测试</span>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-for="i in 6" :key="i">
          <div class="grid-item">
            <h4>列 {{ i }}</h4>
            <p>xs:24 sm:12 md:8 lg:6 xl:4</p>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 表格测试 -->
    <el-card>
      <template #header>
        <span>表格响应式测试</span>
      </template>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="名称" min-width="120" />
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '正常' ? 'success' : 'warning'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default>
            <el-button size="small" type="primary">编辑</el-button>
            <el-button size="small" type="danger">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 表单测试 -->
    <el-card>
      <template #header>
        <span>表单响应式测试</span>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <el-form label-width="80px">
            <el-form-item label="姓名">
              <el-input placeholder="请输入姓名" />
            </el-form-item>
            <el-form-item label="邮箱">
              <el-input placeholder="请输入邮箱" />
            </el-form-item>
            <el-form-item label="状态">
              <el-select placeholder="请选择状态" style="width: 100%">
                <el-option label="正常" value="normal" />
                <el-option label="禁用" value="disabled" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :xs="24" :sm="12" :md="16">
          <el-form label-width="80px">
            <el-form-item label="描述">
              <el-input type="textarea" :rows="6" placeholder="请输入描述" />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </el-card>

    <!-- 按钮测试 -->
    <el-card>
      <template #header>
        <span>按钮测试</span>
      </template>
      <div class="button-group">
        <el-button type="primary">主要按钮</el-button>
        <el-button type="success">成功按钮</el-button>
        <el-button type="warning">警告按钮</el-button>
        <el-button type="danger">危险按钮</el-button>
        <el-button type="info">信息按钮</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

const screenWidth = ref(window.innerWidth)
const screenHeight = ref(window.innerHeight)
const containerWidth = ref(0)

const deviceType = computed(() => {
  if (screenWidth.value < 480) return '手机'
  if (screenWidth.value < 768) return '小平板'
  if (screenWidth.value < 1024) return '平板'
  if (screenWidth.value < 1200) return '小桌面'
  return '桌面'
})

const tableData = [
  { id: 1, name: '测试项目1', description: '这是一个测试项目的描述信息', status: '正常' },
  { id: 2, name: '测试项目2', description: '这是另一个测试项目的描述信息', status: '异常' },
  { id: 3, name: '测试项目3', description: '这是第三个测试项目的描述信息', status: '正常' },
]

const updateScreenSize = () => {
  screenWidth.value = window.innerWidth
  screenHeight.value = window.innerHeight
  
  // 获取容器宽度
  const container = document.querySelector('.page-container')
  if (container) {
    containerWidth.value = container.clientWidth
  }
}

onMounted(() => {
  window.addEventListener('resize', updateScreenSize)
  updateScreenSize()
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenSize)
})
</script>

<style scoped>
.style-test {
  width: 100%;
}

.grid-item {
  background: #f0f9ff;
  border: 1px solid #e0e7ff;
  padding: 20px;
  text-align: center;
  border-radius: 4px;
  margin-bottom: 10px;
}

.grid-item h4 {
  margin: 0 0 10px 0;
  color: #1e40af;
}

.grid-item p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
  }
  
  .button-group .el-button {
    width: 100%;
  }
}
</style>
