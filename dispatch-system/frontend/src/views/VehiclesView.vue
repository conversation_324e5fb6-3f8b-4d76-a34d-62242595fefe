<template>
  <Layout>
    <div class="vehicles">
      <div class="page-header">
        <h1>车辆管理</h1>
        <el-button type="primary">添加车辆</el-button>
      </div>
      <el-card>
        <p>车辆管理页面</p>
      </el-card>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import Layout from '@/components/Layout.vue'
</script>

<style scoped>
.vehicles {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}
</style>
