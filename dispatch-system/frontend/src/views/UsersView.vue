<template>
  <Layout>
    <div class="users">
      <div class="page-header">
        <h1>用户管理</h1>
        <el-button type="primary">添加用户</el-button>
      </div>
      <el-card>
        <p>用户管理页面</p>
      </el-card>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import Layout from '@/components/Layout.vue'
</script>

<style scoped>
.users {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}
</style>
