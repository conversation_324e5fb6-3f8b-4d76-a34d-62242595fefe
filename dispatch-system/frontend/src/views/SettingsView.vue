<template>
  <Layout>
    <div class="settings">
      <div class="page-header">
        <h1>系统设置</h1>
      </div>
      <el-card>
        <p>系统设置页面</p>
      </el-card>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import Layout from '@/components/Layout.vue'
</script>

<style scoped>
.settings {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}
</style>
