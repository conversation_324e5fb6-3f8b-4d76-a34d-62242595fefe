<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.pending }}</h3>
              <p>待处理工单</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon processing">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.processing }}</h3>
              <p>处理中工单</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.completed }}</h3>
              <p>已完成工单</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.total }}</h3>
              <p>总工单数</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="16">
        <el-card>
          <template #header>
            <span>最近工单</span>
          </template>
          <el-table :data="recentOrders" style="width: 100%">
            <el-table-column prop="id" label="工单号" width="100" />
            <el-table-column prop="title" label="标题" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="150" />
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>
          <div class="quick-actions">
            <el-button type="primary" :icon="Plus" @click="$router.push('/work-orders/create')">
              创建工单
            </el-button>
            <el-button type="success" :icon="View" @click="$router.push('/work-orders')">
              查看工单
            </el-button>
            <el-button type="info" :icon="User" @click="$router.push('/personnel')">
              人员管理
            </el-button>
            <el-button type="warning" :icon="Setting" @click="$router.push('/settings')">
              系统设置
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Clock, Loading, Check, Document, Plus, View, User, Setting } from '@element-plus/icons-vue'

const stats = ref({
  pending: 0,
  processing: 0,
  completed: 0,
  total: 0
})

const recentOrders = ref([
  {
    id: 'WO001',
    title: '设备维修',
    status: 'pending',
    created_at: '2024-01-15'
  },
  {
    id: 'WO002',
    title: '清洁服务',
    status: 'processing',
    created_at: '2024-01-14'
  },
  {
    id: 'WO003',
    title: '安全检查',
    status: 'completed',
    created_at: '2024-01-13'
  }
])

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成'
  }
  return texts[status] || status
}

onMounted(() => {
  // 模拟数据加载
  stats.value = {
    pending: 12,
    processing: 8,
    completed: 45,
    total: 65
  }
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.pending {
  background: #e6a23c;
}

.stat-icon.processing {
  background: #409eff;
}

.stat-icon.completed {
  background: #67c23a;
}

.stat-icon.total {
  background: #909399;
}

.stat-info h3 {
  margin: 0;
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.stat-info p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 14px;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.quick-actions .el-button {
  width: 100%;
  justify-content: flex-start;
}
</style>
