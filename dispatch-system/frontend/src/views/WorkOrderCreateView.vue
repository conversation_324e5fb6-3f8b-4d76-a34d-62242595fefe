<template>
  <Layout>
    <div class="work-order-create">
      <div class="page-header">
        <h1>创建工单</h1>
        <el-button @click="$router.back()">返回</el-button>
      </div>
      
      <el-card>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
        >
          <el-form-item label="工单标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入工单标题" />
          </el-form-item>
          
          <el-form-item label="工单描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="4"
              placeholder="请输入工单描述"
            />
          </el-form-item>
          
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="form.priority" placeholder="请选择优先级">
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
              <el-option label="紧急" value="urgent" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="负责人" prop="assignee_id">
            <el-select v-model="form.assignee_id" placeholder="请选择负责人" clearable>
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="user.username"
                :value="user.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="截止日期" prop="due_date">
            <el-date-picker
              v-model="form.due_date"
              type="datetime"
              placeholder="请选择截止日期"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>

          <!-- 自定义字段 -->
          <div v-if="customFields.length > 0" class="custom-fields">
            <el-divider content-position="left">自定义字段</el-divider>

            <el-form-item
              v-for="field in customFields"
              :key="field.id"
              :label="field.label"
              :prop="`custom_fields.${field.name}`"
              :rules="field.required ? [{ required: true, message: `请填写${field.label}`, trigger: 'blur' }] : []"
            >
              <!-- 文本输入 -->
              <el-input
                v-if="field.field_type === 'text'"
                v-model="form.custom_fields[field.name]"
                :placeholder="`请输入${field.label}`"
              />

              <!-- 多行文本 -->
              <el-input
                v-else-if="field.field_type === 'textarea'"
                v-model="form.custom_fields[field.name]"
                type="textarea"
                :rows="3"
                :placeholder="`请输入${field.label}`"
              />

              <!-- 数字输入 -->
              <el-input-number
                v-else-if="field.field_type === 'number'"
                v-model="form.custom_fields[field.name]"
                :placeholder="`请输入${field.label}`"
                style="width: 100%"
              />

              <!-- 日期选择 -->
              <el-date-picker
                v-else-if="field.field_type === 'date'"
                v-model="form.custom_fields[field.name]"
                type="date"
                :placeholder="`请选择${field.label}`"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />

              <!-- 下拉选择 -->
              <el-select
                v-else-if="field.field_type === 'select'"
                v-model="form.custom_fields[field.name]"
                :placeholder="`请选择${field.label}`"
                style="width: 100%"
              >
                <el-option
                  v-for="option in field.options"
                  :key="option"
                  :label="option"
                  :value="option"
                />
              </el-select>

              <!-- 布尔值 -->
              <el-switch
                v-else-if="field.field_type === 'boolean'"
                v-model="form.custom_fields[field.name]"
              />
            </el-form-item>
          </div>

          <el-form-item>
            <el-button type="primary" @click="handleSubmit" :loading="loading">
              创建工单
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import Layout from '@/components/Layout.vue'
import { createWorkOrder, getWorkOrderFields, type CreateWorkOrderData, type WorkOrderField } from '@/api/workOrders'
import { getUsers, type User } from '@/api/users'

const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)
const users = ref<User[]>([])
const customFields = ref<WorkOrderField[]>([])

const form = reactive({
  title: '',
  description: '',
  priority: 'medium',
  assignee_id: null,
  due_date: '',
  custom_fields: {} as Record<string, any>
})

const rules: FormRules = {
  title: [
    { required: true, message: '请输入工单标题', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入工单描述', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

const loadUsers = async () => {
  try {
    const response = await getUsers({ per_page: 100 })
    users.value = response.users.filter(user => user.is_active)
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  }
}

const loadCustomFields = async () => {
  try {
    const response = await getWorkOrderFields()
    customFields.value = response.fields.filter(field => field.is_active)

    // 初始化自定义字段的默认值
    customFields.value.forEach(field => {
      if (field.field_type === 'boolean') {
        form.custom_fields[field.name] = false
      } else {
        form.custom_fields[field.name] = ''
      }
    })
  } catch (error) {
    console.error('加载自定义字段失败:', error)
    ElMessage.error('加载自定义字段失败')
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const data: CreateWorkOrderData = {
      title: form.title,
      description: form.description,
      priority: form.priority,
      assignee_id: form.assignee_id || undefined,
      due_date: form.due_date || undefined,
      custom_fields: form.custom_fields
    }

    await createWorkOrder(data)
    ElMessage.success('工单创建成功')
    router.push('/work-orders')
  } catch (error) {
    console.error('创建工单失败:', error)
    ElMessage.error('创建工单失败')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  formRef.value?.resetFields()
}

onMounted(() => {
  loadUsers()
  loadCustomFields()
})
</script>

<style scoped>
.work-order-create {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.custom-fields {
  margin-top: 20px;
}

.custom-fields .el-divider {
  margin: 20px 0;
}
</style>
