<template>
  <div class="work-order-create">
    <div class="page-header">
      <h2>创建工单</h2>
      <el-button @click="$router.back()">返回</el-button>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        @submit.prevent="handleSubmit"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工单标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入工单标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="form.priority" placeholder="请选择优先级">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工单类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择工单类型">
                <el-option label="维修" value="maintenance" />
                <el-option label="清洁" value="cleaning" />
                <el-option label="安全检查" value="safety" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="assignee_id">
              <el-select v-model="form.assignee_id" placeholder="请选择负责人">
                <el-option
                  v-for="person in personnel"
                  :key="person.id"
                  :label="person.name"
                  :value="person.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="工单描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述工单内容"
          />
        </el-form-item>

        <el-form-item label="预计完成时间" prop="expected_completion">
          <el-date-picker
            v-model="form.expected_completion"
            type="datetime"
            placeholder="选择预计完成时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="位置信息" prop="location">
          <el-input v-model="form.location" placeholder="请输入具体位置" />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="3"
            placeholder="其他备注信息"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            创建工单
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance } from 'element-plus'

const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)

const form = reactive({
  title: '',
  description: '',
  type: '',
  priority: 'medium',
  assignee_id: '',
  expected_completion: '',
  location: '',
  notes: ''
})

const rules = {
  title: [
    { required: true, message: '请输入工单标题', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入工单描述', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择工单类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  assignee_id: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ]
}

const personnel = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' }
])

const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // 这里应该调用API创建工单
        // await api.createWorkOrder(form)
        
        ElMessage.success('工单创建成功')
        router.push('/work-orders')
      } catch (error: any) {
        ElMessage.error(error.message || '创建失败')
      } finally {
        loading.value = false
      }
    }
  })
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const loadPersonnel = async () => {
  try {
    // 这里应该调用API获取人员列表
    // const response = await api.getPersonnel()
    // personnel.value = response.data
  } catch (error) {
    ElMessage.error('加载人员列表失败')
  }
}

onMounted(() => {
  loadPersonnel()
})
</script>

<style scoped>
.work-order-create {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}
</style>
