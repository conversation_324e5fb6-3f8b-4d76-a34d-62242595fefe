<template>
  <div class="work-order-detail page-container">
    <div class="page-header">
      <h2>工单详情</h2>
      <div>
        <el-button @click="$router.back()">返回</el-button>
        <el-button type="primary" @click="editWorkOrder">编辑</el-button>
      </div>
    </div>

    <el-row :gutter="20" v-loading="loading">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <el-tag :type="getStatusType(workOrder.status)">
                {{ getStatusText(workOrder.status) }}
              </el-tag>
            </div>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="工单号">{{ workOrder.id }}</el-descriptions-item>
            <el-descriptions-item label="标题">{{ workOrder.title }}</el-descriptions-item>
            <el-descriptions-item label="类型">{{ getTypeText(workOrder.type) }}</el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityType(workOrder.priority)">
                {{ getPriorityText(workOrder.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="负责人">{{ workOrder.assignee }}</el-descriptions-item>
            <el-descriptions-item label="创建人">{{ workOrder.creator }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ workOrder.created_at }}</el-descriptions-item>
            <el-descriptions-item label="预计完成时间">{{ workOrder.expected_completion }}</el-descriptions-item>
            <el-descriptions-item label="位置" :span="2">{{ workOrder.location }}</el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">{{ workOrder.description }}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{{ workOrder.notes || '无' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card style="margin-top: 20px;">
          <template #header>
            <span>处理记录</span>
          </template>
          
          <el-timeline>
            <el-timeline-item
              v-for="record in processRecords"
              :key="record.id"
              :timestamp="record.created_at"
              placement="top"
            >
              <el-card>
                <h4>{{ record.action }}</h4>
                <p>{{ record.description }}</p>
                <p class="operator">操作人：{{ record.operator }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card>
          <template #header>
            <span>状态操作</span>
          </template>
          
          <div class="status-actions">
            <el-button
              v-if="workOrder.status === 'pending'"
              type="primary"
              @click="updateStatus('processing')"
            >
              开始处理
            </el-button>
            <el-button
              v-if="workOrder.status === 'processing'"
              type="success"
              @click="updateStatus('completed')"
            >
              完成工单
            </el-button>
            <el-button
              v-if="workOrder.status !== 'completed'"
              type="warning"
              @click="updateStatus('pending')"
            >
              重置为待处理
            </el-button>
          </div>
        </el-card>

        <el-card style="margin-top: 20px;">
          <template #header>
            <span>添加处理记录</span>
          </template>
          
          <el-form @submit.prevent="addRecord">
            <el-form-item>
              <el-input
                v-model="newRecord.description"
                type="textarea"
                :rows="4"
                placeholder="请输入处理记录..."
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="addRecord">添加记录</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const loading = ref(false)

const workOrder = ref({
  id: 'WO001',
  title: '设备维修',
  description: '空调设备需要维修，制冷效果不佳',
  type: 'maintenance',
  status: 'processing',
  priority: 'high',
  assignee: '张三',
  creator: '管理员',
  location: '办公楼3楼会议室',
  created_at: '2024-01-15 10:30:00',
  expected_completion: '2024-01-16 18:00:00',
  notes: '需要专业维修工具'
})

const processRecords = ref([
  {
    id: 1,
    action: '工单创建',
    description: '工单已创建，等待处理',
    operator: '管理员',
    created_at: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    action: '开始处理',
    description: '已安排维修人员前往现场',
    operator: '张三',
    created_at: '2024-01-15 14:20:00'
  }
])

const newRecord = reactive({
  description: ''
})

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成'
  }
  return texts[status] || status
}

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    low: 'info',
    medium: 'warning',
    high: 'danger'
  }
  return types[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高'
  }
  return texts[priority] || priority
}

const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    maintenance: '维修',
    cleaning: '清洁',
    safety: '安全检查',
    other: '其他'
  }
  return texts[type] || type
}

const loadWorkOrder = async () => {
  loading.value = true
  try {
    const id = route.params.id
    // 这里应该调用API获取工单详情
    // const response = await api.getWorkOrder(id)
    // workOrder.value = response.data
  } catch (error) {
    ElMessage.error('加载工单详情失败')
  } finally {
    loading.value = false
  }
}

const updateStatus = async (status: string) => {
  try {
    // 这里应该调用API更新工单状态
    // await api.updateWorkOrderStatus(workOrder.value.id, status)
    
    workOrder.value.status = status
    ElMessage.success('状态更新成功')
    
    // 重新加载处理记录
    loadWorkOrder()
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

const addRecord = async () => {
  if (!newRecord.description.trim()) {
    ElMessage.warning('请输入处理记录')
    return
  }

  try {
    // 这里应该调用API添加处理记录
    // await api.addProcessRecord(workOrder.value.id, newRecord)
    
    ElMessage.success('记录添加成功')
    newRecord.description = ''
    
    // 重新加载处理记录
    loadWorkOrder()
  } catch (error) {
    ElMessage.error('添加记录失败')
  }
}

const editWorkOrder = () => {
  router.push(`/work-orders/${workOrder.value.id}/edit`)
}

onMounted(() => {
  loadWorkOrder()
})
</script>

<style scoped>
.work-order-detail {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.operator {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
}
</style>
