<template>
  <Layout>
    <div class="work-order-detail">
      <!-- 页面头部 -->
      <div class="page-header">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/work-orders' }">工单管理</el-breadcrumb-item>
          <el-breadcrumb-item>工单详情</el-breadcrumb-item>
        </el-breadcrumb>

        <div class="page-actions">
          <el-button @click="$router.back()">返回</el-button>
          <el-button
            type="primary"
            @click="$router.push(`/work-orders/${route.params.id}/edit`)"
            v-if="workOrder && workOrder.status !== 'completed'"
          >
            编辑工单
          </el-button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>

      <!-- 工单详情 -->
      <div v-else-if="workOrder" class="work-order-content">
        <!-- 基本信息 -->
        <el-card class="info-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h2>{{ workOrder.title }}</h2>
              <el-tag
                :type="getStatusType(workOrder.status)"
                size="large"
              >
                {{ getStatusText(workOrder.status) }}
              </el-tag>
            </div>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="工单编号">
              {{ workOrder.order_number }}
            </el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityType(workOrder.priority)">
                {{ getPriorityText(workOrder.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建人">
              {{ workOrder.creator_name }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDate(workOrder.created_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="分配给" v-if="workOrder.assignee_name">
              {{ workOrder.assignee_name }}
            </el-descriptions-item>
            <el-descriptions-item label="预计完成时间" v-if="workOrder.due_date">
              {{ formatDate(workOrder.due_date) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 描述信息 -->
        <el-card class="info-card" shadow="hover">
          <template #header>
            <h3>工单描述</h3>
          </template>
          <div class="description-content">
            {{ workOrder.description || '暂无描述' }}
          </div>
        </el-card>

        <!-- 自定义字段 -->
        <el-card v-if="workOrder.custom_fields && Object.keys(workOrder.custom_fields).length > 0" class="info-card" shadow="hover">
          <template #header>
            <h3>自定义字段</h3>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item
              v-for="(value, key) in workOrder.custom_fields"
              :key="key"
              :label="key"
            >
              {{ value }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 操作历史 -->
        <el-card class="info-card" shadow="hover">
          <template #header>
            <h3>操作历史</h3>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="log in workOrder.logs || []"
              :key="log.id"
              :timestamp="formatDate(log.created_at)"
              placement="top"
            >
              <div class="log-content">
                <strong>{{ log.operator_name }}</strong> {{ log.action }}
                <div v-if="log.details" class="log-details">
                  {{ log.details }}
                </div>
              </div>
            </el-timeline-item>
            <el-timeline-item
              :timestamp="formatDate(workOrder.created_at)"
              placement="top"
            >
              <div class="log-content">
                <strong>{{ workOrder.creator_name }}</strong> 创建了工单
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-container">
        <el-result
          icon="warning"
          title="工单不存在"
          sub-title="请检查工单ID是否正确"
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/work-orders')">
              返回工单列表
            </el-button>
          </template>
        </el-result>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import Layout from '@/components/Layout.vue'
import { workOrdersApi } from '@/api/workOrders'

const route = useRoute()
const loading = ref(true)
const workOrder = ref<any>(null)

// 获取工单详情
const fetchWorkOrder = async () => {
  try {
    loading.value = true
    const id = route.params.id as string
    const response = await workOrdersApi.getById(parseInt(id))
    workOrder.value = response.data
  } catch (error: any) {
    console.error('获取工单详情失败:', error)
    ElMessage.error(error.response?.data?.message || '获取工单详情失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': 'warning',
    'in_progress': 'primary',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待处理',
    'in_progress': '进行中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

// 获取优先级类型
const getPriorityType = (priority: string) => {
  const priorityMap: Record<string, string> = {
    'low': 'info',
    'medium': 'warning',
    'high': 'danger'
  }
  return priorityMap[priority] || 'info'
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const priorityMap: Record<string, string> = {
    'low': '低',
    'medium': '中',
    'high': '高'
  }
  return priorityMap[priority] || priority
}

onMounted(() => {
  fetchWorkOrder()
})
</script>

<style scoped>
.work-order-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.description-content {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.log-content {
  color: #606266;
}

.log-details {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 14px;
  color: #909399;
}

.loading-container {
  padding: 20px;
}

.error-container {
  padding: 40px 20px;
  text-align: center;
}
</style>