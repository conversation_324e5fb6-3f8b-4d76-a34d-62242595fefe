<template>
  <div class="personnel page-container">
    <div class="page-header">
      <h2>人员管理</h2>
      <el-button type="primary" :icon="Plus" @click="showAddDialog = true">
        添加人员
      </el-button>
    </div>

    <el-card>
      <el-table :data="personnel" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="position" label="职位" />
        <el-table-column prop="department" label="部门" />
        <el-table-column prop="phone" label="电话" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
              {{ scope.row.status === 'active' ? '在职' : '离职' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="editPerson(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deletePerson(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingPerson ? '编辑人员' : '添加人员'"
      width="500px"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="职位" prop="position">
          <el-input v-model="form.position" />
        </el-form-item>
        <el-form-item label="部门" prop="department">
          <el-input v-model="form.department" />
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="form.phone" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status">
            <el-option label="在职" value="active" />
            <el-option label="离职" value="inactive" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const loading = ref(false)
const showAddDialog = ref(false)
const editingPerson = ref<any>(null)
const formRef = ref<FormInstance>()

const personnel = ref([
  {
    id: 1,
    name: '张三',
    position: '维修工',
    department: '维修部',
    phone: '13800138001',
    email: '<EMAIL>',
    status: 'active'
  },
  {
    id: 2,
    name: '李四',
    position: '清洁工',
    department: '清洁部',
    phone: '13800138002',
    email: '<EMAIL>',
    status: 'active'
  }
])

const form = reactive({
  name: '',
  position: '',
  department: '',
  phone: '',
  email: '',
  status: 'active'
})

const rules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
  department: [{ required: true, message: '请输入部门', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入电话', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

const loadPersonnel = async () => {
  loading.value = true
  try {
    // 这里应该调用API获取人员数据
    // const response = await api.getPersonnel()
    // personnel.value = response.data
  } catch (error) {
    ElMessage.error('加载人员数据失败')
  } finally {
    loading.value = false
  }
}

const editPerson = (person: any) => {
  editingPerson.value = person
  Object.assign(form, person)
  showAddDialog.value = true
}

const deletePerson = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个人员吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 这里应该调用API删除人员
    // await api.deletePerson(id)
    
    ElMessage.success('删除成功')
    loadPersonnel()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (editingPerson.value) {
          // 编辑
          // await api.updatePerson(editingPerson.value.id, form)
          ElMessage.success('更新成功')
        } else {
          // 添加
          // await api.createPerson(form)
          ElMessage.success('添加成功')
        }
        
        showAddDialog.value = false
        resetForm()
        loadPersonnel()
      } catch (error: any) {
        ElMessage.error(error.message || '操作失败')
      }
    }
  })
}

const resetForm = () => {
  editingPerson.value = null
  Object.assign(form, {
    name: '',
    position: '',
    department: '',
    phone: '',
    email: '',
    status: 'active'
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

onMounted(() => {
  loadPersonnel()
})
</script>

<style scoped>
.personnel {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}
</style>
