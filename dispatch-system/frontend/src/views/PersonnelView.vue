<template>
  <Layout>
    <div class="personnel">
      <div class="page-header">
        <h1>人员管理</h1>
        <el-button type="primary">添加人员</el-button>
      </div>
      <el-card>
        <p>人员管理页面</p>
      </el-card>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import Layout from '@/components/Layout.vue'
</script>

<style scoped>
.personnel {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}
</style>
