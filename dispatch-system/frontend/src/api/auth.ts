import api from './index'

export interface LoginData {
  username: string
  password: string
}

export interface User {
  id: number
  username: string
  email: string
  role: string
  is_active: boolean
  group_id?: number
  group_name?: string
  created_at: string
  updated_at: string
}

export interface LoginResponse {
  access_token: string
  user: User
}

// 用户登录
export const login = (data: LoginData): Promise<LoginResponse> => {
  return api.post('/auth/login', data)
}

// 获取用户信息
export const getProfile = (): Promise<{ user: User }> => {
  return api.get('/auth/profile')
}

// 修改密码
export const changePassword = (data: {
  old_password: string
  new_password: string
}): Promise<{ message: string }> => {
  return api.post('/auth/change-password', data)
}
