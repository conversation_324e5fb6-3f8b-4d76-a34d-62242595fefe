import api from './index'

export interface User {
  id: number
  username: string
  email: string
  role: 'admin' | 'manager' | 'user'
  is_active: boolean
  group_id?: number
  group_name?: string
  created_at: string
  updated_at: string
}

export interface CreateUserData {
  username: string
  email: string
  password: string
  role: string
  group_id?: number
  is_active?: boolean
}

export interface UpdateUserData {
  username?: string
  email?: string
  role?: string
  group_id?: number
  is_active?: boolean
}

export interface UserListParams {
  page?: number
  per_page?: number
  search?: string
  role?: string
  group_id?: number
  is_active?: boolean
}

export interface UserListResponse {
  users: User[]
  total: number
  page: number
  per_page: number
  pages: number
}

// 获取用户列表
export const getUsers = (params: UserListParams = {}): Promise<UserListResponse> => {
  return api.get('/users', { params })
}

// 获取用户详情
export const getUser = (id: number): Promise<{ user: User }> => {
  return api.get(`/users/${id}`)
}

// 创建用户
export const createUser = (data: CreateUserData): Promise<{ user: User }> => {
  return api.post('/users', data)
}

// 更新用户
export const updateUser = (id: number, data: UpdateUserData): Promise<{ user: User }> => {
  return api.put(`/users/${id}`, data)
}

// 删除用户
export const deleteUser = (id: number): Promise<{ message: string }> => {
  return api.delete(`/users/${id}`)
}

// 重置用户密码
export const resetUserPassword = (id: number, newPassword: string): Promise<{ message: string }> => {
  return api.post(`/users/${id}/reset-password`, { new_password: newPassword })
}
