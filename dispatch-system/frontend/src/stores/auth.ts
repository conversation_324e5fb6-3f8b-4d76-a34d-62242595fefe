import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { login as loginApi, getProfile, type User, type LoginData } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))

  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isManager = computed(() => user.value?.role === 'manager')
  const isUser = computed(() => user.value?.role === 'user')

  // 登录
  const login = async (loginData: LoginData) => {
    try {
      const response = await loginApi(loginData)
      token.value = response.access_token
      user.value = response.user

      localStorage.setItem('token', response.access_token)
      localStorage.setItem('user', JSON.stringify(response.user))

      return response
    } catch (error) {
      throw error
    }
  }

  // 登出
  const logout = () => {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 初始化用户信息
  const initUser = async () => {
    const savedUser = localStorage.getItem('user')
    if (savedUser && token.value) {
      try {
        user.value = JSON.parse(savedUser)
        // 验证token是否有效
        await getProfile()
      } catch (error) {
        logout()
      }
    }
  }

  // 更新用户信息
  const updateUser = (userData: User) => {
    user.value = userData
    localStorage.setItem('user', JSON.stringify(userData))
  }

  return {
    user,
    token,
    isLoggedIn,
    isAdmin,
    isManager,
    isUser,
    login,
    logout,
    initUser,
    updateUser
  }
})
