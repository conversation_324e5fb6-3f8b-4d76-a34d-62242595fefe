import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

// 配置axios基础URL - 从环境变量获取
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api'
axios.defaults.baseURL = API_BASE_URL

export interface User {
  id: number
  username: string
  email: string
  role: string
  name: string
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem('token'))
  const user = ref<User | null>(null)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isManager = computed(() => user.value?.role === 'manager')

  // 设置认证头
  const setAuthHeader = (authToken: string) => {
    axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`
  }

  // 清除认证头
  const clearAuthHeader = () => {
    delete axios.defaults.headers.common['Authorization']
  }

  // 登录
  const login = async (username: string, password: string) => {
    try {
      const response = await axios.post('/auth/login', {
        username,
        password
      })

      const { access_token, user: userData } = response.data
      
      token.value = access_token
      user.value = userData
      
      localStorage.setItem('token', access_token)
      setAuthHeader(access_token)
      
      return userData
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '登录失败')
    }
  }

  // 登出
  const logout = () => {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
    clearAuthHeader()
  }

  // 初始化用户信息
  const initUser = async () => {
    if (!token.value) return

    try {
      setAuthHeader(token.value)
      const response = await axios.get('/auth/me')
      user.value = response.data
    } catch (error) {
      // 如果获取用户信息失败，清除token
      logout()
      throw error
    }
  }

  // 注册
  const register = async (userData: {
    username: string
    email: string
    password: string
    name: string
    role?: string
  }) => {
    try {
      const response = await axios.post('/auth/register', userData)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '注册失败')
    }
  }

  // 更新用户信息
  const updateProfile = async (profileData: Partial<User>) => {
    try {
      const response = await axios.put('/auth/profile', profileData)
      user.value = { ...user.value, ...response.data }
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新失败')
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      await axios.put('/auth/change-password', {
        old_password: oldPassword,
        new_password: newPassword
      })
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '密码修改失败')
    }
  }

  // 初始化时设置认证头
  if (token.value) {
    setAuthHeader(token.value)
  }

  return {
    token,
    user,
    isLoggedIn,
    isAdmin,
    isManager,
    login,
    logout,
    initUser,
    register,
    updateProfile,
    changePassword
  }
})
